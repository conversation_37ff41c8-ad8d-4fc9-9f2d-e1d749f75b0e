import socket
import json
from datetime import datetime
from typing import List, Dict, Any, Union

# https://huggingface.co/docs/smolagents/index
# https://github.com/huggingface/smolagents
from smolagents import tool, ToolCallingAgent, OpenAIServerModel
from smolagents.monitoring import LogLevel

# https://github.com/derenrich/wikibase-rest-api-client
from wikibase_rest_api_client import Client
from wikibase_rest_api_client.api.search import search_item
from wikibase_rest_api_client.api.sitelinks import get_item_sitelinks
from wikibase_rest_api_client.types import Response
from wikibase_rest_api_client.models import Error, SearchItemResults, ItemSitelinks

# https://github.com/RDFLib/sparqlwrapper/
from SPARQLWrapper import SPARQLWrapper, JSON


wdclient = Client(
    headers={
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    },  # type: ignore
    follow_redirects=True,  # type: ignore
)


def search_wikidata_sitelinks(wikidata_id : str):
    """Search for a wikidata id and return the sitelinks.

    Args:
        wikidata_id: The wikidata id to search for (required)

    Returns:
        A dictionary with the label, aliases and description

    Examples:
        ✅ search_with_wikidata_id("Q2985103")
    """
    response_sl: Response[Union[ItemSitelinks, Error]] = (
        get_item_sitelinks.sync_detailed(wikidata_id, client=wdclient)
    )
    if response_sl.parsed is not None and not isinstance(response_sl.parsed, Error):
        return response_sl.parsed.to_dict()
    return []


def search_wikidata_types(qid_param: str):
    """
    Returns the types (instance of /P31) of a Wikidata QID.
    On Wikidata, each item (Qxxx) is linked to one or more types/classes via the P31 property — "instance of".
    👉 So for a given QID, P31 indicates the instance type (example: Q95 (Google) is an instance of Corporation Q891723).

    Args:
        qid_param: The wikidata id to search for (required)

    Returns:
        A list of types with qid and label

    Examples:
        ✅ get_wikidata_types("Q2985103")
    """

    sparql = SPARQLWrapper("https://query.wikidata.org/sparql")
    sparql.setQuery(f"""
    SELECT ?type ?typeLabel WHERE {{
        wd:{qid_param} wdt:P31 ?type .
        SERVICE wikibase:label {{ bd:serviceParam wikibase:language "fr,en". }}
    }}
    """)
    sparql.setReturnFormat(JSON)

    results_types = sparql.query().convert()
    type_list = []
    for r in results_types["results"]["bindings"]:  # type: ignore
        type_list.append(
            {
                "qid": r["type"]["value"].split("/")[-1],  # type: ignore
                "label": r["typeLabel"]["value"],  # type: ignore
            }
        )
    return type_list


@tool
def search_wikidata_term(search_term: str, lang: str = "fr"):
    """Search for a term in wikidata and return the id, label and description in the specified language.

    Args:
        search_term: The term to search for (required)
        lang: The language to return the label and description in (default: fr). It's a 2-letter ISO 3166-1 alpha-2 code
                Examples: 'FR','US', 'UK', 'GB'

    Returns:
        A list of matches with id, label and description

    Examples:
        ✅ search_wikidata_term("Paris")
        ✅ search_wikidata_term("Paris", "fr")
    """
    response_si: Response[Union[SearchItemResults, Error]] = search_item.sync_detailed(
        q=search_term, language=lang, client=wdclient
    )

    matches = []
    if response_si.parsed is not None and not isinstance(response_si.parsed, Error):
        for r in getattr(response_si.parsed, "results", []):
            if r.display_label.value.lower() == search_term.lower():
                sitelinks = len(search_wikidata_sitelinks(r.id))
                entity_types = []
                for entity_type in search_wikidata_types(r.id):
                    entity_types.append(
                        {"qid": entity_type["qid"], "label": entity_type["label"]}
                    )
                matches.append(
                    {
                        "id": r.id,
                        "label": r.display_label.value,
                        "description": r.description.value,
                        "types": entity_types,
                        "sitelinks": sitelinks,
                    }
                )
    else:
        if response_si.parsed is not None:
            return [
                {
                    "error": getattr(response_si.parsed, "error", "Unknown error"),
                    "details": getattr(
                        response_si.parsed, "message", "No details provided"
                    ),
                }
            ]
        else:
            return [
                {
                    "error": "Unknown error",
                    "details": "No details provided",
                }
            ]

    return matches


if __name__ == "__main__":

    print(search_wikidata_term("Python"))
    exit(0)


    lmstudio_model = OpenAIServerModel(
        model_id="local-model",  # This can be any name, LM Studio will use whatever model you have loaded
        api_base="http://localhost:1234/v1",
        api_key="not-needed",
        temperature=0.1,
    )

    agents_manager = ToolCallingAgent(
        tools=[
            search_wikidata_term,
        ],
        model=lmstudio_model,
        max_steps=10,
        stream_outputs=True,
        verbosity_level=LogLevel.ERROR,
    )

    result = agents_manager.run(
        """
        Quelle heure est-il à Paris et quel est le taux de change de l'euro vers le dollar ?
        En 2025, quels sont les jours fériés en France ?
        Indique moi unepetite blague.
        """
    )

    print(result)
