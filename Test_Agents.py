import datetime
import pytz

# https://github.com/psf/requests
import requests

# https://huggingface.co/docs/smolagents/index
# https://github.com/huggingface/smolagents
from smolagents import tool, ToolCallingAgent, OpenAIServerModel
from smolagents.monitoring import LogLevel


@tool
def get_public_holidays(country_code: str, year: int) -> str:
    """Fetch public holidays for a given country and year (no API key).
    Args:
        country_code: ISO 3166-1 alpha-2 code (e.g., "US", "BR", "GB").
        year: Four-digit year to query (e.g., 2025).
    Returns:
        A newline-separated list like "YYYY-MM-DD: Local Name". If none found,
        returns a short message describing the issue.
    """
    try:
        url = (
            f"https://date.nager.at/api/v3/PublicHolidays/{year}/{country_code.upper()}"
        )
        r = requests.get(url, timeout=10)
        r.raise_for_status()
        holidays = r.json()
        if not holidays:
            return f"No holidays found for {country_code.upper()} in {year}."
        lines = [f"{h['date']}: {h['localName']}" for h in holidays]
        return "\n".join(lines)
    except requests.RequestException as e:
        return f"Error fetching holidays: {e}"


@tool
def get_current_time_in_timezone(timezone: str) -> str:
    """
    Obtient l'heure actuelle dans un fuseau horaire spécifié.

    Args:
        timezone (str): Le fuseau horaire (ex: 'Europe/Paris')

    Returns:
        str: L'heure actuelle formatée
    """
    try:
        tz = pytz.timezone(timezone)
        local_time = datetime.datetime.now(tz).strftime("%Y-%m-%d %H:%M:%S")
        return f"The current local time in {timezone} is: {local_time}"
    except pytz.UnknownTimeZoneError as e:
        return f"Error: Unknown timezone '{timezone}': {str(e)}"
    except (ValueError, TypeError) as e:
        return f"Error fetching time for timezone '{timezone}': {str(e)}"


@tool
def get_eur_exchange_rate(currency_code: str) -> str:
    """A tool that retrieves the current exchange rate from EUR to a given currency

    Args:
        currency_code: The 3-letter currency code in lowercase (e.g., "usd", "gbp", "jpy")
    """

    try:
        api_url = "https://cdn.jsdelivr.net/npm/@fawazahmed0/currency-api@latest/v1/currencies/eur.json"
        response = requests.get(api_url, timeout=10)
        response.raise_for_status()

        data = response.json()

        date = data.get("date", "Unknown date")
        exchange_rates = data.get("eur", {})

        if currency_code in exchange_rates:
            rate = exchange_rates[currency_code]
            return f"1 EUR = {rate} {currency_code.upper()} (as of {date})"
        else:
            return f"Currency code '{currency_code}' not found in the exchange rate data. Please check the currency code format."
    except requests.RequestException as e:
        return f"Error fetching exchange rate data: {str(e)}"
    except ValueError as e:
        return f"Error parsing exchange rate data: {str(e)}"


@tool
def get_simple_joke(lang: str = "fr") -> str:
    """
    A smolagent tool that fetches a random joke from JokeAPI.

    This tool retrieves jokes from the JokeAPI service (https://v2.jokeapi.dev/)
    without any content filtering. It can handle both single-line jokes and
    two-part jokes (setup + delivery).

    Args:
        lang (str, optional): Language preference for the joke. Defaults to "fr".

    Returns:
        str: A formatted joke string. For single jokes, returns the joke directly.
             For two-part jokes, returns both setup and delivery formatted together.
             Returns an error message if the API request fails or joke cannot be parsed.

    Raises:
        No exceptions are raised - all errors are caught and returned as error messages.

    Examples:
        >>> get_simple_joke()
        "Here's a joke:\n\nWhy don't scientists trust atoms?\nBecause they make up everything!"

        >>> get_simple_joke("en")
        "Here's a joke:\n\nI told my wife she was drawing her eyebrows too high.\nShe looked surprised."

    Note:
        - All jokes are fetched from the '/joke/any' endpoint without category filtering

    API Reference:
        More information about JokeAPI: https://v2.jokeapi.dev/
        An API Python Wrapper for Sv443's JokeAPI: https://github.com/benjhar/JokeAPI-Python
    """

    try:
        api_url = f"https://v2.jokeapi.dev/joke/any?lang={lang}"

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:77.0) Gecko/20100101 Firefox/77.0",
            "Accept-Encoding": "gzip"
        }
        response = requests.get(
            url=api_url,
            headers=headers,
            timeout=10
        )
        response.raise_for_status()

        joke_data = response.json()

        if joke_data.get("error", False):
            return f"Error: {joke_data.get('message', 'Could not get joke')}"

        if joke_data.get("type") == "single":
            return f"Here's a joke:\n\n{joke_data.get('joke', '')}"
        elif joke_data.get("type") == "twopart":
            setup = joke_data.get("setup", "")
            delivery = joke_data.get("delivery", "")
            return f"Here's a joke:\n\n{setup}\n{delivery}"

        return "Could not parse joke from API response"
    except requests.RequestException as e:
        return f"Error fetching joke: {str(e)}"
    except ValueError as e:
        return f"Error parsing joke data: {str(e)}"


if __name__ == "__main__":
    lmstudio_model = OpenAIServerModel(
        model_id="local-model",  # This can be any name, LM Studio will use whatever model you have loaded
        api_base="http://localhost:1234/v1",
        api_key="not-needed",
        temperature=0.1,
    )

    """
    Le paramètre stream_outputs fonctionne en combinaison avec verbosity_level :
    Différents niveaux de verbosité disponibles :
     LogLevel.OFF = -1     # Aucun affichage
     LogLevel.ERROR = 0    # Seulement les erreurs
     LogLevel.INFO = 1     # Affichage normal (par défaut)
     LogLevel.DEBUG = 2    # Affichage détaillé

        stream_outputs	verbosity_level	Résultat
        False	        LogLevel.OFF	Aucun affichage pendant l'exécution, résultat final seulement
        False	        LogLevel.ERROR	Erreurs + résultat final
        False	        LogLevel.INFO	Quelques infos + résultat final
        True	        LogLevel.OFF	Interface streaming mais sans logs détaillés
        True	        LogLevel.INFO	Interface streaming complète (votre cas initial)
        True	        LogLevel.DEBUG	Interface streaming + logs très détaillés

    Recommandations d'usage
        Pour le développement :
            stream_outputs=True,
            verbosity_level=LogLevel.INFO
        Pour la production :
            stream_outputs=False,
            verbosity_level=LogLevel.ERROR
        Pour le débogage :
            stream_outputs=True,
            verbosity_level=LogLevel.DEBUG
    """

    agents_manager = ToolCallingAgent(
        tools=[get_public_holidays, get_current_time_in_timezone, get_eur_exchange_rate, get_simple_joke],
        model=lmstudio_model,
        max_steps=10,
        stream_outputs=True,
        verbosity_level=LogLevel.ERROR,
    )

    result = agents_manager.run(
        """
        Quelle heure est-il à Paris et quel est le taux de change de l'euro vers le dollar ?
        En 2025, quels sont les jours fériés en France ?
        Indique moi unepetite blague.
        """
    )

    print(result)
