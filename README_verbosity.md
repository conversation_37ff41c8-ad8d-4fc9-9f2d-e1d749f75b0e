# Contrôle de la Verbosité dans SmolaAgents

Ce document explique comment réduire ou supprimer l'affichage verbose dans vos agents SmolaAgents.

## Problème Initial

Votre script affichait beaucoup d'informations de débogage comme :
```
─────────────────────────────────────────────────────────────────────────────────────── New run ────────────────────────────────────────────────────────────────────────────────────────╮
│ Quelle heure est-il à Paris et quel est le taux de change de l'euro vers le dollar ?                                                                                                   │
╰─ OpenAIServerModel - local-model ──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ Step 1 ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
{"tool": "get_current_time_in_timezone", "arguments": "{"timezone":"Europe/Paris"}"}
```

## Solutions

### 1. Solution Rapide (Recommandée)

Modifiez votre `ToolCallingAgent` avec ces paramètres :

```python
from smolagents.monitoring import LogLevel

agents_manager = ToolCallingAgent(
    tools=[...],
    model=lmstudio_model,
    max_steps=10,
    stream_outputs=False,        # Désactive le streaming
    verbosity_level=LogLevel.OFF # Désactive tous les logs
)
```

### 2. Niveaux de Verbosité Disponibles

```python
from smolagents.monitoring import LogLevel

# LogLevel.OFF = -1     # Aucun affichage (silencieux complet)
# LogLevel.ERROR = 0    # Seulement les erreurs
# LogLevel.INFO = 1     # Affichage normal (par défaut)
# LogLevel.DEBUG = 2    # Affichage détaillé (très verbose)
```

### 3. Paramètres Importants

#### `stream_outputs`
- `True` : Affiche les outputs en temps réel (mode verbose)
- `False` : Affiche seulement le résultat final

#### `verbosity_level`
- Contrôle le niveau de détail des logs
- `LogLevel.OFF` pour un mode complètement silencieux

## Exemples d'Usage

### Mode Silencieux (Production)
```python
agent = ToolCallingAgent(
    tools=[...],
    model=model,
    stream_outputs=False,
    verbosity_level=LogLevel.OFF
)
```

### Mode Erreurs Seulement (Débogage Léger)
```python
agent = ToolCallingAgent(
    tools=[...],
    model=model,
    stream_outputs=False,
    verbosity_level=LogLevel.ERROR
)
```

### Mode Normal (Développement)
```python
agent = ToolCallingAgent(
    tools=[...],
    model=model,
    stream_outputs=False,
    verbosity_level=LogLevel.INFO
)
```

### Mode Debug (Débogage Complet)
```python
agent = ToolCallingAgent(
    tools=[...],
    model=model,
    stream_outputs=True,
    verbosity_level=LogLevel.DEBUG
)
```

## Fichiers Modifiés

1. **SEOAgents.py** : Version principale avec mode silencieux
2. **SEOAgents_examples.py** : Exemples avec différents niveaux de verbosité

## Test

Pour tester la version silencieuse :
```bash
python SEOAgents.py
```

Pour tester différents niveaux :
```bash
python SEOAgents_examples.py
```

## Résultat

Avec `LogLevel.OFF` et `stream_outputs=False`, vous obtenez seulement :
```
Il est actuellement 13:18 h à Paris.

Taux de change EUR→USD (2025‑09‑23) : 1 EUR = 1,18179903 USD.

Jours fériés en France en 2025 :
- 01 janvier : Jour de l'an
- 21 avril : Lundi de Pâques
...

Blague :
> Quand est‑ce que les chiens aboient ?
> Lorsqu'ils ont un chat dans la gorge.
```

Aucun affichage de débogage, juste le résultat final !
