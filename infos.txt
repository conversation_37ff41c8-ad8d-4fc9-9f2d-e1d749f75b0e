
    """
    from smolagents.agents import PromptTemplates, PlanningPromptTemplate, ManagedAgentPromptTemplate, FinalAnswerPromptTemplate, EMPTY_PROMPT_TEMPLATES

    EMPTY_PROMPT_TEMPLATES
        prompt_templates=PromptTemplates(
            system_prompt="Tu es un agent spécialisé dans la récupération de données variées. Tu dois être aussi précis que possible."
            planning=PlanningPromptTemplate(
                initial_plan="Tu dois créer un plan pour répondre à la tâche. Le plan doit être détaillé et étape par étape.",
            ),
            managed_agent=ManagedAgentPromptTemplate(
                task="Tu dois exécuter le plan pour répondre à la tâche. Tu dois être aussi précis que possible."
            )
            final_answer=FinalAnswerPromptTemplate(
                pre_messages="Tu dois fournir une réponse finale à la tâche. Tu dois être aussi précis que possible."
            )
    )"""

    """
    # Create a LiteLLM model with Ollama
    ollama_model = LiteLLMModel(
        model_id="ollama_chat/gpt-oss:20b",
        api_base="https://ollama.com:443",
        api_key="447d9b4a99e745478aa557a1421b8448.sw7ANALKpswZmgEltzRctsGX",
        num_ctx=8192,
    )

    agent = ToolCallingAgent(
        tools=[get_public_holidays],
        model=ollama_model
        #model="lm-studio/lmstudio-ai-llm-13b"
    )
    """

    """
        prompt_templates=PromptTemplates(
            system_prompt="",
            planning=PlanningPromptTemplate(
                initial_plan="",
                update_plan_pre_messages="",
                update_plan_post_messages="",
            ),
            managed_agent=ManagedAgentPromptTemplate(task="", report=""),
            final_answer=FinalAnswerPromptTemplate(pre_messages="", post_messages=""),
        )
    """