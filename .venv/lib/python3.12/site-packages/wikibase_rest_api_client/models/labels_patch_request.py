from typing import TYPE_CHECKING, Any, Dict, List, Type, TypeVar, Union, cast

from attrs import define as _attrs_define
from attrs import field as _attrs_field

from ..types import UNSET, Unset

if TYPE_CHECKING:
    from ..models.patch_document_patch_item import PatchDocumentPatchItem


T = TypeVar("T", bound="LabelsPatchRequest")


@_attrs_define
class LabelsPatchRequest:
    """
    Attributes:
        patch (List['PatchDocumentPatchItem']): A JSON Patch document as defined by RFC 6902
        tags (Union[Unset, List[str]]):  Example: ['mobile edit', 'external tool edit'].
        bot (Union[Unset, bool]):  Default: False.
        comment (Union[Unset, str]):  Example: API edit fixing the modelling as discussed in ....
    """

    patch: List["PatchDocumentPatchItem"]
    tags: Union[Unset, List[str]] = UNSET
    bot: Union[Unset, bool] = False
    comment: Union[Unset, str] = UNSET
    additional_properties: Dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        patch = []
        for patch_item_data in self.patch:
            patch_item = patch_item_data.to_dict()
            patch.append(patch_item)

        tags: Union[Unset, List[str]] = UNSET
        if not isinstance(self.tags, Unset):
            tags = self.tags

        bot = self.bot

        comment = self.comment

        field_dict: Dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update(
            {
                "patch": patch,
            }
        )
        if tags is not UNSET:
            field_dict["tags"] = tags
        if bot is not UNSET:
            field_dict["bot"] = bot
        if comment is not UNSET:
            field_dict["comment"] = comment

        return field_dict

    @classmethod
    def from_dict(cls: Type[T], src_dict: Dict[str, Any]) -> T:
        from ..models.patch_document_patch_item import PatchDocumentPatchItem

        d = src_dict.copy()
        patch = []
        _patch = d.pop("patch")
        for patch_item_data in _patch:
            patch_item = PatchDocumentPatchItem.from_dict(patch_item_data)

            patch.append(patch_item)

        tags = cast(List[str], d.pop("tags", UNSET))

        bot = d.pop("bot", UNSET)

        comment = d.pop("comment", UNSET)

        labels_patch_request = cls(
            patch=patch,
            tags=tags,
            bot=bot,
            comment=comment,
        )

        labels_patch_request.additional_properties = d
        return labels_patch_request

    @property
    def additional_keys(self) -> List[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
