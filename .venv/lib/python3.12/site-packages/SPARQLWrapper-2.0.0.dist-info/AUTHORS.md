# Authors

* <PERSON> ([@iherman](http://github.com/iherman))
* <PERSON> ([@wikier](http://github.com/wikier))
* <PERSON> ([@dayures](http://github.com/dayures))
* <PERSON><PERSON> ([@indeyets](http://github.com/indeyets))

# Contributors

See https://github.com/RDFLib/sparqlwrapper/graphs/contributors

* [@eggplants]https://github.com/eggplants: most things to make 2.0.0 happen
* <PERSON><PERSON> ([@ArthurLiu](http://github.com/ArthurLiu)): different patches
* <PERSON> ([@cmlenz](http://github.com/cmlenz)): feature to allow developers to choose the json module
* Pēteris <PERSON>ne ([@cuu508](http://github.com/cuu508)): great feedback and patches
* <PERSON><PERSON><PERSON> ([<EMAIL>](mailto:<EMAIL>)), patch for the query regular expresion
* <PERSON> ([@wwaites](http://github.com/wwaites)): patches for RDFLib3
* <PERSON> ([@cburgmer](http://github.com/cburgmer)): patches for RDFLib3
* Thomas Kluyver ([@takluyver](http://github.com/takluyver)): patches for Python 3.x
* Diego Berrueta ([@berrueta](http://github.com/berrueta)): new function for printing results as table
* Olivier Berger ([@olberger](http://github.com/olberger)): patch regarding raw response for unknown formats
* Benjamin Cogrel ([@bcogrel](http://github.com/bcogrel)): standard query types
* Urs Holzer ([@uholzer](http://github.com/uholzer)): features, patches and testing
* Alf Lervåg ([@alf](http://github.com/alf)): setup patch
* Nolan Nichols ([@nicholsn](http://github.com/nicholsn)): http disgest auth support
* Kevin Turner ([@keturn](https://github.com/keturn)): `SmartWrapper.Value.__repr__()` implementation
* Marcelo Jorge Vieira ([@marcelometal](https://github.com/marcelometal)): typos
* Trevor Andersen ([@trevorandersen](https://github.com/trevorandersen): patches for Python 3.x
* Carlos Martinez-Ortiz ([@cmartinez](https://github.com/cmartinez): improves support for return format HTTP parameter
* Christian Amsüss ([@chrysn](https://github.com/chrysn)): dependecy fixes
* Chris Lamb ([@lamby](https://github.com/lamby)): typo
* Hugo van Kemenade ([@hugovk](https://github.com/hugovk)): update classifiers (Python 3.6)
* Edward Betts ([@EdwardBetts](https://github.com/EdwardBetts)): Correct spelling mistakes
* Carlos Martínez ([@c-martinez](https://github.com/c-martinez)): Mainly support for CSV and TSV results in SPARQL SELECT queries
* Dan Michael O. Heggø ([@danmichaelo](https://github.com/danmichaelo)): update README with SPARQLWrapper2 example
* Sam Clements ([@borntyping](https://github.com/borntyping)): Provide hints about setting properly the timeout
* Marc Feger ([@MaFeg100](https://github.com/MaFeg100)): Improve/tests for development
