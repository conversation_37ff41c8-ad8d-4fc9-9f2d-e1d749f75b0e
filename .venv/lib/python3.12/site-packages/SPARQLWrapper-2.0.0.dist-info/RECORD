../../../bin/rqw,sha256=w_4GBCkFyLo9zZJl8AmIxtskc8h8UTUqGEc3FNgP0Ak,225
SPARQLWrapper-2.0.0.dist-info/AUTHORS.md,sha256=7oV4hamlTbjfsaWy15f3BVH2h90Nf5mJ-rR0Z1azy9s,2725
SPARQLWrapper-2.0.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
SPARQLWrapper-2.0.0.dist-info/LICENSE.txt,sha256=Z1IX12CEodcefDAOAMJ7irELJAX-huUCOiuzio5G8Ik,2134
SPARQLWrapper-2.0.0.dist-info/METADATA,sha256=kU92L4KNVjo9aP6-jm4FXVAUpNScd5mIWWbIGHu_D_I,2020
SPARQLWrapper-2.0.0.dist-info/RECORD,,
SPARQLWrapper-2.0.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
SPARQLWrapper-2.0.0.dist-info/WHEEL,sha256=G16H4A3IeoQmnOrYV4ueZGKSjhipXx8zc8nu9FGlvMA,92
SPARQLWrapper-2.0.0.dist-info/entry_points.txt,sha256=aIYAzonEA7winfiw8NydOLNu406HC6aRBlKLI2H5kEQ,48
SPARQLWrapper-2.0.0.dist-info/top_level.txt,sha256=3KluNiTwOkX16hLJwC3UEYzKdEscknK--UV5q56mYWY,14
SPARQLWrapper/KeyCaseInsensitiveDict.py,sha256=JF83-6EPbcm9F4gg0GQ11vTVuLzdJ7sDsubEP9j-3zw,1377
SPARQLWrapper/SPARQLExceptions.py,sha256=qFlU175hp61gO6bvgQsCdSTEGOFnJwJNBQlIGS5W7-o,2595
SPARQLWrapper/SmartWrapper.py,sha256=GxZiMGZpGppPZX54W-YdUtcdAAa83GJjPLdyfLWPK-4,15557
SPARQLWrapper/Wrapper.py,sha256=M9lTPkpvRU2xAUbrHiKYK0mEV8pkycNS3lPoO__0gSE,58238
SPARQLWrapper/__init__.py,sha256=6kU9hD9FnlFbk2c8uFkpGb1arB3268nN74RUh91e60s,1213
SPARQLWrapper/__pycache__/KeyCaseInsensitiveDict.cpython-312.pyc,,
SPARQLWrapper/__pycache__/SPARQLExceptions.cpython-312.pyc,,
SPARQLWrapper/__pycache__/SmartWrapper.cpython-312.pyc,,
SPARQLWrapper/__pycache__/Wrapper.cpython-312.pyc,,
SPARQLWrapper/__pycache__/__init__.cpython-312.pyc,,
SPARQLWrapper/__pycache__/main.cpython-312.pyc,,
SPARQLWrapper/__pycache__/sparql_dataframe.cpython-312.pyc,,
SPARQLWrapper/main.py,sha256=MKNPMrFxIGN_A7-UwyMS_AycjswscgKsP37h2K2df8k,4330
SPARQLWrapper/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
SPARQLWrapper/sparql_dataframe.py,sha256=-oM7_eXbwGgeNkFv9mSxe3JWHM3xQQk90nNrbhthnrI,2429
