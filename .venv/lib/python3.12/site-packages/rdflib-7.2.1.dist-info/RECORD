../../../bin/csv2rdf,sha256=VQgC9wn_SNv96uCrNox0EqIgg33lB1PvKHmWHtWqQf4,227
../../../bin/rdf2dot,sha256=uNtj8ZpCdqBk-qtA-pNL1DOUe0BZacVqPZL4YCmbVqw,227
../../../bin/rdfgraphisomorphism,sha256=sq1Csqc6SEyx42ZWnQas5MIkzLnyiEMX2lNr0hhy63I,236
../../../bin/rdfpipe,sha256=yVEEzf8Q3v19vkJyvURqrQ8HYZArA9406y3R9hwuggY,227
../../../bin/rdfs2dot,sha256=R7d0pnpHTrHxTIZeknrZLcF5nnwTqetlKahu3FT5CQc,228
rdflib-7.2.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
rdflib-7.2.1.dist-info/LICENSE,sha256=8pQbC6z8Rsa_4TzZjKtorbeENXTDeCmX46J2TNVFviM,1524
rdflib-7.2.1.dist-info/METADATA,sha256=eL9nansk8Cez1W_H-MjrX2eeWms1zzAUTl6pA-9uXsM,11734
rdflib-7.2.1.dist-info/RECORD,,
rdflib-7.2.1.dist-info/WHEEL,sha256=Nq82e9rUAnEjt98J6MlVmMCZb-t9cYE2Ir1kpBmnWfs,88
rdflib-7.2.1.dist-info/entry_points.txt,sha256=UJ2UwT8m9TvV7UrdJM2fcEREwExL_RQVEfnID-Qnkdk,212
rdflib/__init__.py,sha256=LD7MmnHPPMPQFb6_HT88IJOd1HoE7OIJWC3CyqBnFoA,4810
rdflib/__pycache__/__init__.cpython-312.pyc,,
rdflib/__pycache__/_networking.cpython-312.pyc,,
rdflib/__pycache__/_type_checking.cpython-312.pyc,,
rdflib/__pycache__/collection.cpython-312.pyc,,
rdflib/__pycache__/compare.cpython-312.pyc,,
rdflib/__pycache__/compat.cpython-312.pyc,,
rdflib/__pycache__/container.cpython-312.pyc,,
rdflib/__pycache__/events.cpython-312.pyc,,
rdflib/__pycache__/exceptions.cpython-312.pyc,,
rdflib/__pycache__/graph.cpython-312.pyc,,
rdflib/__pycache__/parser.cpython-312.pyc,,
rdflib/__pycache__/paths.cpython-312.pyc,,
rdflib/__pycache__/plugin.cpython-312.pyc,,
rdflib/__pycache__/query.cpython-312.pyc,,
rdflib/__pycache__/resource.cpython-312.pyc,,
rdflib/__pycache__/serializer.cpython-312.pyc,,
rdflib/__pycache__/store.cpython-312.pyc,,
rdflib/__pycache__/term.cpython-312.pyc,,
rdflib/__pycache__/util.cpython-312.pyc,,
rdflib/__pycache__/void.cpython-312.pyc,,
rdflib/__pycache__/xsd_datetime.cpython-312.pyc,,
rdflib/_networking.py,sha256=h7fmP4F-UOBl13LgwohSVZ-eFhlOvBmQtFCtn7BunmM,4625
rdflib/_type_checking.py,sha256=3DX4uvHqtemcA22WRyvh5dHfhXCI-rdAju7W9siJQug,910
rdflib/collection.py,sha256=ps9jitftHUJY8qtah0nh0sWf7nAe_mGDWJaXsFbAvaQ,9984
rdflib/compare.py,sha256=QBVk0M0PNzNqWQJdyujPK4ndmtDkKvZF-rN9N8oOP2U,21980
rdflib/compat.py,sha256=lmdF6eDnroUYERcZsYrr9xEvXfaiD8-hdAEKoeqvk9w,2406
rdflib/container.py,sha256=ZBpM2gRKlBPPgcxiKhABDDU4C6-tr5fEI0hB2fYrHRw,8227
rdflib/events.py,sha256=YIzmlMEzEH_geUyYVCx8il-ucQQdZJiCiciEuTxRnCU,2945
rdflib/exceptions.py,sha256=5LYvnkGqs0snG8nRe9OF9jndaJnd54ayKS2slH7ahZc,850
rdflib/extras/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
rdflib/extras/__pycache__/__init__.cpython-312.pyc,,
rdflib/extras/__pycache__/cmdlineutils.cpython-312.pyc,,
rdflib/extras/__pycache__/describer.cpython-312.pyc,,
rdflib/extras/__pycache__/external_graph_libs.cpython-312.pyc,,
rdflib/extras/__pycache__/infixowl.cpython-312.pyc,,
rdflib/extras/__pycache__/shacl.cpython-312.pyc,,
rdflib/extras/cmdlineutils.py,sha256=5AH_JNF--DRZWSXh5PoHi4lm9nivXnioz1w1pMrkCxE,1948
rdflib/extras/describer.py,sha256=-Pu7QuA5kJBY6Chv-2hFULsBuU4d8YBBQ1WWKDymlpw,9272
rdflib/extras/external_graph_libs.py,sha256=sFoMcAwQynQfyTxwj31KAVo3uQZ3xPvxZZ1_dmOIkBo,11926
rdflib/extras/infixowl.py,sha256=4RiZpBWZehLEjrP1sU4L76xDiBTz-JsyGR9Tmx4nIKw,80793
rdflib/extras/shacl.py,sha256=7h62KQbun5pDMmq3PVek2IF_4XFY3MrheAGHeDile9I,8313
rdflib/graph.py,sha256=g72ZddikNMIfW366d_1xDzL6IoipxxGonhcsYo1lJ0k,115356
rdflib/namespace/_BRICK.py,sha256=XsMA9-_01jbMR1T9l-3600q4K1v4TZU8nAn7pjbiIKA,126990
rdflib/namespace/_CSVW.py,sha256=veMbUAm24GONxs1Kdk1VMat65niKWEPySlqcT8YxLQg,12976
rdflib/namespace/_DC.py,sha256=mjA91wYdEvT-Hx79RHZgcpMB_SVw_yjwj98F6_iD4vg,1673
rdflib/namespace/_DCAM.py,sha256=JxvGJe95380fDIvZsgUhIGYGI9aIF7aF_4hf6RjY63Y,887
rdflib/namespace/_DCAT.py,sha256=pHlF5yFsUAu6RqfbyvULir6tiQpDTCiNzPGwIbGEiYs,5474
rdflib/namespace/_DCMITYPE.py,sha256=MC2f0ChRgWho8hS9Rv1dGO3LY_g1WP1WPkwcYfh6d6c,1337
rdflib/namespace/_DCTERMS.py,sha256=4HDU5fid7Z0v6MOUuZpvvHnNQ3MzMeboizsfgJg4qxA,10012
rdflib/namespace/_DOAP.py,sha256=wk_uK25tvoAjwJSieIpiUeg1iJoCq6yd33D2fPWokSQ,14664
rdflib/namespace/_FOAF.py,sha256=io2arNzBsiYtHXJVxz9UdhljSPNFEkC4L_YmWqTomqI,6247
rdflib/namespace/_GEO.py,sha256=wQZK_UEQNK47HqcKVUdUcuj-jTaYsoMtNNC3wC3ktBY,9137
rdflib/namespace/_ODRL2.py,sha256=yeDIBtPymmv0cwsxoW22OogQibcBkMl1OC4IZH2ECYs,22158
rdflib/namespace/_ORG.py,sha256=zhobrfzEmjam_W0JeZKKJxIcvID3SE1TcWKjrD8CIuo,13207
rdflib/namespace/_OWL.py,sha256=Q_A272osHglNHzvJVaaR0-h31TuJBeLKS2TS7NM5R7w,10480
rdflib/namespace/_PROF.py,sha256=QKYhG7ulMe_6wKS5Ye6FZ_oghKeVmo7Qdcupk7vMlyQ,2807
rdflib/namespace/_PROV.py,sha256=DNq0vTDBa9Vh4rXMVnz5VJ8vIRSPltHn8nhljdyStes,24148
rdflib/namespace/_QB.py,sha256=bPegKsZ_iZh47HPoeRqIN2k1FXNYG6kITECKaqN75vw,5413
rdflib/namespace/_RDF.py,sha256=icPqdMkOC1ukMJDyGFR6nWdC7KsMp1cuHX2_587SXnc,2249
rdflib/namespace/_RDFS.py,sha256=Gw0zTb0B0VTn2pvwI3bQYEqjH91JguG_XSy3den7DDA,1488
rdflib/namespace/_SDO.py,sha256=iCWwLFWqoupbQK7d2k_7zqD84SZ90LAHijexqTU47kU,424141
rdflib/namespace/_SH.py,sha256=dMK9rpvD9JqrVssb3jYUniuOQjF4Ug4C-3nK86ahPPQ,23321
rdflib/namespace/_SKOS.py,sha256=qnTVyb31WxMoFf2vRcEnAYXJXaEwsVpCNKeUMl73_d8,4715
rdflib/namespace/_SOSA.py,sha256=BzuIlQSzNgaN5jYMQaR0HA4COX68wsROl2gOI2in8v8,7117
rdflib/namespace/_SSN.py,sha256=T9JKgBQdipzcKc3o6xwz4wM9WLI-avhywRXROqCbHkM,3213
rdflib/namespace/_TIME.py,sha256=JiSNziV0A4r8KMstVppHgeY8ZHzDzQhzwU7lHybpblI,12936
rdflib/namespace/_VANN.py,sha256=pKantDMzEu_wfOFT7zXYIhFlsgTJu_ylgv5PCpRGLJE,1232
rdflib/namespace/_VOID.py,sha256=Nyv3GnciRwLLu6aNL-feMbrRuYv4fU66deCwDVCFSjA,4689
rdflib/namespace/_WGS.py,sha256=HPXnL6MqFJ8b6uVl_QdgG456dZLKfwH89UpAC7G58Pc,633
rdflib/namespace/_XSD.py,sha256=vPN0Cg5PcZzqIj4NdwhFiYOJuiHYbCUb5v-tBsWAQGs,6276
rdflib/namespace/__init__.py,sha256=4CygT9SOx0RW4Hq6sUn_xs0TF5I-WaVXpYySysYioVE,34459
rdflib/namespace/__pycache__/_BRICK.cpython-312.pyc,,
rdflib/namespace/__pycache__/_CSVW.cpython-312.pyc,,
rdflib/namespace/__pycache__/_DC.cpython-312.pyc,,
rdflib/namespace/__pycache__/_DCAM.cpython-312.pyc,,
rdflib/namespace/__pycache__/_DCAT.cpython-312.pyc,,
rdflib/namespace/__pycache__/_DCMITYPE.cpython-312.pyc,,
rdflib/namespace/__pycache__/_DCTERMS.cpython-312.pyc,,
rdflib/namespace/__pycache__/_DOAP.cpython-312.pyc,,
rdflib/namespace/__pycache__/_FOAF.cpython-312.pyc,,
rdflib/namespace/__pycache__/_GEO.cpython-312.pyc,,
rdflib/namespace/__pycache__/_ODRL2.cpython-312.pyc,,
rdflib/namespace/__pycache__/_ORG.cpython-312.pyc,,
rdflib/namespace/__pycache__/_OWL.cpython-312.pyc,,
rdflib/namespace/__pycache__/_PROF.cpython-312.pyc,,
rdflib/namespace/__pycache__/_PROV.cpython-312.pyc,,
rdflib/namespace/__pycache__/_QB.cpython-312.pyc,,
rdflib/namespace/__pycache__/_RDF.cpython-312.pyc,,
rdflib/namespace/__pycache__/_RDFS.cpython-312.pyc,,
rdflib/namespace/__pycache__/_SDO.cpython-312.pyc,,
rdflib/namespace/__pycache__/_SH.cpython-312.pyc,,
rdflib/namespace/__pycache__/_SKOS.cpython-312.pyc,,
rdflib/namespace/__pycache__/_SOSA.cpython-312.pyc,,
rdflib/namespace/__pycache__/_SSN.cpython-312.pyc,,
rdflib/namespace/__pycache__/_TIME.cpython-312.pyc,,
rdflib/namespace/__pycache__/_VANN.cpython-312.pyc,,
rdflib/namespace/__pycache__/_VOID.cpython-312.pyc,,
rdflib/namespace/__pycache__/_WGS.cpython-312.pyc,,
rdflib/namespace/__pycache__/_XSD.cpython-312.pyc,,
rdflib/namespace/__pycache__/__init__.cpython-312.pyc,,
rdflib/parser.py,sha256=-L6ICHjJmfh48c6XYronS-vGtc7IUiCfbvae-ZDR6-A,27922
rdflib/paths.py,sha256=paWIOfe1kzASeFglLB3wQtjtPfUvjc-AjDboPHi99oM,21574
rdflib/plugin.py,sha256=OxrWq--ks70qn2cr4DCJd_QBb-BKcjgkmf9oxitKdEE,13018
rdflib/plugins/__init__.py,sha256=39nGkLLAy54hfMDO9jCpUoaAcVeDdmKCKB3xRQ7KnkA,111
rdflib/plugins/__pycache__/__init__.cpython-312.pyc,,
rdflib/plugins/parsers/RDFVOC.py,sha256=WTqXCoT8Lu3o58z6IqECgu30aKumUGDRQX4iApKgZSE,481
rdflib/plugins/parsers/__init__.py,sha256=ekSQ4YBr0bT86j5BplDSgH-SoHHF3gZP_xShRG6Z6Dk,9
rdflib/plugins/parsers/__pycache__/RDFVOC.cpython-312.pyc,,
rdflib/plugins/parsers/__pycache__/__init__.cpython-312.pyc,,
rdflib/plugins/parsers/__pycache__/hext.cpython-312.pyc,,
rdflib/plugins/parsers/__pycache__/jsonld.cpython-312.pyc,,
rdflib/plugins/parsers/__pycache__/notation3.cpython-312.pyc,,
rdflib/plugins/parsers/__pycache__/nquads.cpython-312.pyc,,
rdflib/plugins/parsers/__pycache__/ntriples.cpython-312.pyc,,
rdflib/plugins/parsers/__pycache__/patch.cpython-312.pyc,,
rdflib/plugins/parsers/__pycache__/rdfxml.cpython-312.pyc,,
rdflib/plugins/parsers/__pycache__/trig.cpython-312.pyc,,
rdflib/plugins/parsers/__pycache__/trix.cpython-312.pyc,,
rdflib/plugins/parsers/hext.py,sha256=1J4ZexQ2lrqgwip-7c5JOqBWAR0961XMmFmtEJuT1xE,6459
rdflib/plugins/parsers/jsonld.py,sha256=EFfkXMiVgt_9FSXpddUoMi8NUyr-TcBx9MI3SMESHuk,24464
rdflib/plugins/parsers/notation3.py,sha256=ku8sSdnRBILtD5fZ_CbNZr9IVnI0JQi62CGZEwnmlck,67310
rdflib/plugins/parsers/nquads.py,sha256=Cfj7oKSMnlaFkjCjfaclbPCgDFF6Y3teM4ntHJnxLDs,5092
rdflib/plugins/parsers/ntriples.py,sha256=oJEJMa7ZKjrWmCTGEQK6isIUJQDB2SEE4PoukEQ1_4I,12690
rdflib/plugins/parsers/patch.py,sha256=4svNTg51HFJbV5Ky1u1HLAvCk2D29A69UsmXeMK67J4,6714
rdflib/plugins/parsers/rdfxml.py,sha256=d2TewVI003KFh8sqip0cMPLGXvmjy_31C6QmcSlHssg,26115
rdflib/plugins/parsers/trig.py,sha256=_KKmS5XYnGMv8pqoVBNTo9LyLjNTcr1GKkOyb0jscMU,5428
rdflib/plugins/parsers/trix.py,sha256=gh_msqDxG8xuiNUUree42KbDJUHRjmqp9ZbjwY3kBQk,10078
rdflib/plugins/serializers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
rdflib/plugins/serializers/__pycache__/__init__.cpython-312.pyc,,
rdflib/plugins/serializers/__pycache__/hext.cpython-312.pyc,,
rdflib/plugins/serializers/__pycache__/jsonld.cpython-312.pyc,,
rdflib/plugins/serializers/__pycache__/longturtle.cpython-312.pyc,,
rdflib/plugins/serializers/__pycache__/n3.cpython-312.pyc,,
rdflib/plugins/serializers/__pycache__/nquads.cpython-312.pyc,,
rdflib/plugins/serializers/__pycache__/nt.cpython-312.pyc,,
rdflib/plugins/serializers/__pycache__/patch.cpython-312.pyc,,
rdflib/plugins/serializers/__pycache__/rdfxml.cpython-312.pyc,,
rdflib/plugins/serializers/__pycache__/trig.cpython-312.pyc,,
rdflib/plugins/serializers/__pycache__/trix.cpython-312.pyc,,
rdflib/plugins/serializers/__pycache__/turtle.cpython-312.pyc,,
rdflib/plugins/serializers/__pycache__/xmlwriter.cpython-312.pyc,,
rdflib/plugins/serializers/hext.py,sha256=xc__dMPlKO5beQqQLWFD2wPyldht72t6B2r805TG1Bo,7549
rdflib/plugins/serializers/jsonld.py,sha256=3lEKgQeJEcN7iUktezhRM3-MyKaVGQ7VPgCUqkvIb_A,15351
rdflib/plugins/serializers/longturtle.py,sha256=FUamlbPC6kl-R3ppPn1Er8wHeiNOc1DMuif4AGtY1Ko,11153
rdflib/plugins/serializers/n3.py,sha256=BYiEPX6kCyL49zmlW2LkGsqrYTacVx6wrbZak0Liluc,3105
rdflib/plugins/serializers/nquads.py,sha256=9Na0JfLO-8IGcIhR3D9C6H0SgAc5LIwlkxnEDSMGdUo,1837
rdflib/plugins/serializers/nt.py,sha256=GJOw3cdCji2ff3dPyMx65CIIsmSqFAiRV_5PO716Yqc,3131
rdflib/plugins/serializers/patch.py,sha256=aok828-wYXwUsY1dp60WisO3BCOqtokoxRrqugvekjU,4089
rdflib/plugins/serializers/rdfxml.py,sha256=KGWzBqkraqp6LVNAbm42DfebjknalmJqR5Dw5cL3q5U,15685
rdflib/plugins/serializers/trig.py,sha256=2t0F4dLVSnXuTIrE2z9hP7GW3j9nWVBC2NxMj2gM8T8,3980
rdflib/plugins/serializers/trix.py,sha256=gysxJQwLxTijVs620CRzRqS-BIQIThep9ks1QsdTT_I,3429
rdflib/plugins/serializers/turtle.py,sha256=buMf0jc_VbfZYTVWDmvaRIOl8a4826e5Rqk3aqhqRXQ,15502
rdflib/plugins/serializers/xmlwriter.py,sha256=Cg10lhGxKYyKe71WwTeCJ9RQ7p4VaEol9FagoIBEiJo,4284
rdflib/plugins/shared/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
rdflib/plugins/shared/__pycache__/__init__.cpython-312.pyc,,
rdflib/plugins/shared/jsonld/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
rdflib/plugins/shared/jsonld/__pycache__/__init__.cpython-312.pyc,,
rdflib/plugins/shared/jsonld/__pycache__/context.cpython-312.pyc,,
rdflib/plugins/shared/jsonld/__pycache__/errors.cpython-312.pyc,,
rdflib/plugins/shared/jsonld/__pycache__/keys.cpython-312.pyc,,
rdflib/plugins/shared/jsonld/__pycache__/util.cpython-312.pyc,,
rdflib/plugins/shared/jsonld/context.py,sha256=3fK0EQrfsVD_SGddo-GJM3_mX5Gz9jp0McF4aFW0OYI,23106
rdflib/plugins/shared/jsonld/errors.py,sha256=QGRCaFanGyYmXrw2LNTTYBYklvz_bUG5VyysZFyYxY4,435
rdflib/plugins/shared/jsonld/keys.py,sha256=SHKyniVqNBoV6MtUKmk_ltE2JXoFUbwpi7vtW8tLGrY,534
rdflib/plugins/shared/jsonld/util.py,sha256=VDesnp3mbMpfe0qEPg2mPwCk7QF8JTAyVI_J45J1RQQ,12822
rdflib/plugins/sparql/__init__.py,sha256=7P5HqP5XdcwaX5hrI6n8_Mc0E1pOWU477JJhodyHevY,1379
rdflib/plugins/sparql/__pycache__/__init__.cpython-312.pyc,,
rdflib/plugins/sparql/__pycache__/aggregates.cpython-312.pyc,,
rdflib/plugins/sparql/__pycache__/algebra.cpython-312.pyc,,
rdflib/plugins/sparql/__pycache__/datatypes.cpython-312.pyc,,
rdflib/plugins/sparql/__pycache__/evaluate.cpython-312.pyc,,
rdflib/plugins/sparql/__pycache__/evalutils.cpython-312.pyc,,
rdflib/plugins/sparql/__pycache__/operators.cpython-312.pyc,,
rdflib/plugins/sparql/__pycache__/parser.cpython-312.pyc,,
rdflib/plugins/sparql/__pycache__/parserutils.cpython-312.pyc,,
rdflib/plugins/sparql/__pycache__/processor.cpython-312.pyc,,
rdflib/plugins/sparql/__pycache__/sparql.cpython-312.pyc,,
rdflib/plugins/sparql/__pycache__/update.cpython-312.pyc,,
rdflib/plugins/sparql/aggregates.py,sha256=rmSkZPnikO7dBMkjkWs4g2S_eFjGBdbhkdpIpFWEqyk,10681
rdflib/plugins/sparql/algebra.py,sha256=T-dr_lTEMu7oZCJJtSd74MJDK2B93vUJYJVQW5MBYL8,60999
rdflib/plugins/sparql/datatypes.py,sha256=E8Oo4eIkl1d6rg6wjzjok0ZDeQECygU3bTxEsIz8vHI,2547
rdflib/plugins/sparql/evaluate.py,sha256=XwPACIg8ikVHiSCWJ9-PGo954is7yhB2kCoZtFfpfNY,21814
rdflib/plugins/sparql/evalutils.py,sha256=HUFnd5GJ6_OCV-zI4gidCnHfZHCxD3zN6MtRdr01nQk,4908
rdflib/plugins/sparql/operators.py,sha256=2xv4x_XkmpIwwJor7R7v32neYtdRtBjBaC1tb6skg-Q,36819
rdflib/plugins/sparql/parser.py,sha256=FsKaQ0zTaR3bNPaZqPp4R6xXIaJhudc9r2HuJVlN5dM,51688
rdflib/plugins/sparql/parserutils.py,sha256=K5j6ghKrTwt0_6abCJWG4zK5p7yf5b1hyFqiuwujI7M,9202
rdflib/plugins/sparql/processor.py,sha256=5ECDK2782FULXTmtJbPNPY5XhnpKTbSWw5XuPovAaPM,4936
rdflib/plugins/sparql/results/__init__.py,sha256=4N9RA47kn1q0uOiRJddc_TpU9tiibuoq2pmUG2dio18,58
rdflib/plugins/sparql/results/__pycache__/__init__.cpython-312.pyc,,
rdflib/plugins/sparql/results/__pycache__/csvresults.cpython-312.pyc,,
rdflib/plugins/sparql/results/__pycache__/graph.cpython-312.pyc,,
rdflib/plugins/sparql/results/__pycache__/jsonresults.cpython-312.pyc,,
rdflib/plugins/sparql/results/__pycache__/rdfresults.cpython-312.pyc,,
rdflib/plugins/sparql/results/__pycache__/tsvresults.cpython-312.pyc,,
rdflib/plugins/sparql/results/__pycache__/txtresults.cpython-312.pyc,,
rdflib/plugins/sparql/results/__pycache__/xmlresults.cpython-312.pyc,,
rdflib/plugins/sparql/results/csvresults.py,sha256=qSIlhddaKC6y48PhiwORjFQf6UcG5Je5jmuqlB7vddM,3708
rdflib/plugins/sparql/results/graph.py,sha256=6slf96keAyM3w0stGN9jdJQl2xnPnYSrOv5AcqTX9QQ,530
rdflib/plugins/sparql/results/jsonresults.py,sha256=AXNRvqNph1N8bCKJ6fQy2sfjQQpCPY5HvzMiRiNMzIo,5541
rdflib/plugins/sparql/results/rdfresults.py,sha256=0nGwPcdkyhLXTQGdNdFSO7CWEQJi8nWjrxfVmBO3SFQ,2780
rdflib/plugins/sparql/results/tsvresults.py,sha256=zwrrHeL9Ur1tNv2UuG8jr0Lql_V-l6MwavqRoCA7dXw,3118
rdflib/plugins/sparql/results/txtresults.py,sha256=wU3nVevDaRpVnXF7n2U5nvaqnRvk1bOQ46ZufcVOvZ0,3136
rdflib/plugins/sparql/results/xmlresults.py,sha256=BlvBDWhJjk8ZE8gkRoLstta7V7ZEZUvRXLCIypYSFuc,12236
rdflib/plugins/sparql/sparql.py,sha256=2yjv-n0N7QE2O4d7J5JJ7Xh6hDJAQXbRv-NL8sSBuk0,15830
rdflib/plugins/sparql/update.py,sha256=z-pVF-UGBa54snywD_TQg_OsL9R_7tQfoq3s8H51jB4,11502
rdflib/plugins/stores/__init__.py,sha256=NGddME-fuVX1SBSkNld2FBLchmKR8rNs5mi3EL3Top8,67
rdflib/plugins/stores/__pycache__/__init__.cpython-312.pyc,,
rdflib/plugins/stores/__pycache__/auditable.cpython-312.pyc,,
rdflib/plugins/stores/__pycache__/berkeleydb.cpython-312.pyc,,
rdflib/plugins/stores/__pycache__/concurrent.cpython-312.pyc,,
rdflib/plugins/stores/__pycache__/memory.cpython-312.pyc,,
rdflib/plugins/stores/__pycache__/regexmatching.cpython-312.pyc,,
rdflib/plugins/stores/__pycache__/sparqlconnector.cpython-312.pyc,,
rdflib/plugins/stores/__pycache__/sparqlstore.cpython-312.pyc,,
rdflib/plugins/stores/auditable.py,sha256=8byKEmRbsDolmDhidKPzhiq3BZN7iK7ZDs3yeru7wdw,7872
rdflib/plugins/stores/berkeleydb.py,sha256=moa55SXW4bI45QY3Gq-zSvQwWxtN5L5QfvKGzDFmwPs,29925
rdflib/plugins/stores/concurrent.py,sha256=RRDdaKQPV0MAkV9EjDBoRTf06QRsmBnD2hCQOivVW_w,2719
rdflib/plugins/stores/memory.py,sha256=6jvyGatLBsn-PKf-XmxCzg9mOPkifJ3TlZE9z4dkTO4,30102
rdflib/plugins/stores/regexmatching.py,sha256=4nMbcJN7ayFdll6VhSUmCAWIwMBHBOgU_Yt8LTvQ2Ao,6490
rdflib/plugins/stores/sparqlconnector.py,sha256=p-tK8HWbgHL_6IJLfdsaaQBlU7MQZ7ez4UhvmIw6zKI,6499
rdflib/plugins/stores/sparqlstore.py,sha256=4_txXopE4Dwonuf0mZGHRxrdfPmYpfakXkDvVPZd5Vg,38814
rdflib/py.typed,sha256=KT9WNyn4878ZisihNjjF3Bm2qu8R9s2F5irJrg8vxhQ,66
rdflib/query.py,sha256=Zm-Sdh2ItowA5NOGI7oVv_5XzSe8GYewf04mxwTNHwg,15084
rdflib/resource.py,sha256=3WUi8eORT8pfcibMjMes-1YeaTeo_HKveQ1LQWwmObE,13989
rdflib/serializer.py,sha256=ZoGUQgdvipC2MQX-rDv2Bamgb_rNPVVb8BzaHICUfV0,1309
rdflib/store.py,sha256=8Yz9u6vAfNFMgyUI-hG-6rnksmeStucBge6xJw-SQeM,15744
rdflib/term.py,sha256=a43BOi9sXJWg_53fsb7PjxF6SXFnKFuppN9LYAzuKQM,89231
rdflib/tools/__init__.py,sha256=YzY7Eyz4zw8Ib5fl5bv4bQXzbAbsVxa2LrybTtJDYw0,58
rdflib/tools/__pycache__/__init__.cpython-312.pyc,,
rdflib/tools/__pycache__/chunk_serializer.cpython-312.pyc,,
rdflib/tools/__pycache__/csv2rdf.cpython-312.pyc,,
rdflib/tools/__pycache__/defined_namespace_creator.cpython-312.pyc,,
rdflib/tools/__pycache__/graphisomorphism.cpython-312.pyc,,
rdflib/tools/__pycache__/rdf2dot.cpython-312.pyc,,
rdflib/tools/__pycache__/rdfpipe.cpython-312.pyc,,
rdflib/tools/__pycache__/rdfs2dot.cpython-312.pyc,,
rdflib/tools/chunk_serializer.py,sha256=qU-7Zsfh2zrsviQyLCiRXMKaFfo3LeafS6mEWeXm2YY,4830
rdflib/tools/csv2rdf.py,sha256=f3xbvIQtThwTinEFuoxaIzYk2dDWf_3zbf2U7v5lyvw,16696
rdflib/tools/defined_namespace_creator.py,sha256=0Dns50_gRl7tO7rL6jN2USYAADvjLWFkQlTI8MZfSkw,6719
rdflib/tools/graphisomorphism.py,sha256=8gZxbH5vKhpZ6vIVY8ohJ8SoBRJOMZhQVDkhKMr2ZuA,3411
rdflib/tools/rdf2dot.py,sha256=lKdE1Xi7O8N-4rcUnh1KLtwxsC_Uop03eh-BrV2JCiU,4995
rdflib/tools/rdfpipe.py,sha256=4PZ6ufyXANPTQlEyJm_5IdNAiFlC6xr-gpo_BNHxEHE,5498
rdflib/tools/rdfs2dot.py,sha256=kLf9cK-cC5WECqRRD1S6Vk-6c2-Vk_kniytbxvWf5CY,3783
rdflib/util.py,sha256=5mE34oBp25x3jWTjijAtGCQ7oBOqvwnFK1aOz7xLZcU,17596
rdflib/void.py,sha256=KsD_bmJnJA64XahflTyq1Fm-5EtHkEa-Br4-KLCTm1g,4641
rdflib/xsd_datetime.py,sha256=q_8F2mj9tSb8QARFP6dH40xnKr-Lh7yE7U2jistiezM,26256
