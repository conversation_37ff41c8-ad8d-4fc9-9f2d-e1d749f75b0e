wikibase_rest_api_client-0.2.5.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
wikibase_rest_api_client-0.2.5.dist-info/LICENSE,sha256=UIp30ue1HZit7tMmSK0SS3swJBqOcLLnLJn5LY5YdNE,1036
wikibase_rest_api_client-0.2.5.dist-info/METADATA,sha256=2BbjTQNDCrny1BLb2Pjm4n9FxSFPoBgDdTbS_f7_VZw,5789
wikibase_rest_api_client-0.2.5.dist-info/RECORD,,
wikibase_rest_api_client-0.2.5.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
wikibase_rest_api_client-0.2.5.dist-info/WHEEL,sha256=XbeZDeTWKc1w7CSIyre5aMDU_-PohRwTQceYnisIYYY,88
wikibase_rest_api_client/__init__.py,sha256=2idoxx9T-krRiqwdWnHgp8u3NduNKrtUVq-NLVlK6_E,160
wikibase_rest_api_client/__pycache__/__init__.cpython-312.pyc,,
wikibase_rest_api_client/__pycache__/client.cpython-312.pyc,,
wikibase_rest_api_client/__pycache__/errors.cpython-312.pyc,,
wikibase_rest_api_client/__pycache__/types.cpython-312.pyc,,
wikibase_rest_api_client/api/__init__.py,sha256=zTSiG_ujSjAqWPyc435YXaX9XTlpMjiJWBbV-f-YtdA,45
wikibase_rest_api_client/api/__pycache__/__init__.cpython-312.pyc,,
wikibase_rest_api_client/api/aliases/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
wikibase_rest_api_client/api/aliases/__pycache__/__init__.cpython-312.pyc,,
wikibase_rest_api_client/api/aliases/__pycache__/add_item_aliases_in_language.cpython-312.pyc,,
wikibase_rest_api_client/api/aliases/__pycache__/add_property_aliases_in_language.cpython-312.pyc,,
wikibase_rest_api_client/api/aliases/__pycache__/get_item_aliases.cpython-312.pyc,,
wikibase_rest_api_client/api/aliases/__pycache__/get_item_aliases_in_language.cpython-312.pyc,,
wikibase_rest_api_client/api/aliases/__pycache__/get_property_aliases.cpython-312.pyc,,
wikibase_rest_api_client/api/aliases/__pycache__/get_property_aliases_in_language.cpython-312.pyc,,
wikibase_rest_api_client/api/aliases/__pycache__/patch_item_aliases.cpython-312.pyc,,
wikibase_rest_api_client/api/aliases/__pycache__/patch_property_aliases.cpython-312.pyc,,
wikibase_rest_api_client/api/aliases/add_item_aliases_in_language.py,sha256=akI6L3__vqLIPwBi6HufsVWmchHKm59nFj0uV_o8igI,5135
wikibase_rest_api_client/api/aliases/add_property_aliases_in_language.py,sha256=tct3n9ONvvsZrIpJT82wDclOg7Ir8u3XIBbxvU_NH_c,5122
wikibase_rest_api_client/api/aliases/get_item_aliases.py,sha256=B3_DlQWSTtK4IQlwFHD0btvmcjpjv31VCXVBhPox-uw,4504
wikibase_rest_api_client/api/aliases/get_item_aliases_in_language.py,sha256=pN_9p14U30uq3oA5Y3PM7MepqleqCiTZ8JRdC88HFr0,4785
wikibase_rest_api_client/api/aliases/get_property_aliases.py,sha256=C1m4lyqK3Pmjx-y57hyeRWC_79ljnsKaKE5mJ1K7U_k,4505
wikibase_rest_api_client/api/aliases/get_property_aliases_in_language.py,sha256=nY3DsMMLZ313zwEtRhJrFAPxst-85nvfaNxlw7lyZt0,4750
wikibase_rest_api_client/api/aliases/patch_item_aliases.py,sha256=Oy2prmke2IHiOC4Sf1lKLw1GeCAqKCPrwyzPJOfryg8,3780
wikibase_rest_api_client/api/aliases/patch_property_aliases.py,sha256=wdWAKAGgGSgvdmi9jaZlMUFlNqN4Ymtd6KaiPFJwSRU,3839
wikibase_rest_api_client/api/descriptions/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
wikibase_rest_api_client/api/descriptions/__pycache__/__init__.cpython-312.pyc,,
wikibase_rest_api_client/api/descriptions/__pycache__/delete_item_description.cpython-312.pyc,,
wikibase_rest_api_client/api/descriptions/__pycache__/delete_property_description.cpython-312.pyc,,
wikibase_rest_api_client/api/descriptions/__pycache__/get_item_description.cpython-312.pyc,,
wikibase_rest_api_client/api/descriptions/__pycache__/get_item_descriptions.cpython-312.pyc,,
wikibase_rest_api_client/api/descriptions/__pycache__/get_property_description.cpython-312.pyc,,
wikibase_rest_api_client/api/descriptions/__pycache__/get_property_descriptions.cpython-312.pyc,,
wikibase_rest_api_client/api/descriptions/__pycache__/patch_item_descriptions.cpython-312.pyc,,
wikibase_rest_api_client/api/descriptions/__pycache__/patch_property_descriptions.cpython-312.pyc,,
wikibase_rest_api_client/api/descriptions/__pycache__/replace_item_description.cpython-312.pyc,,
wikibase_rest_api_client/api/descriptions/__pycache__/replace_property_description.cpython-312.pyc,,
wikibase_rest_api_client/api/descriptions/delete_item_description.py,sha256=RDChUvBpLLdR-MPAtOzJZv2QMaZjyY8nHuhnpo-2auM,4564
wikibase_rest_api_client/api/descriptions/delete_property_description.py,sha256=KrRo1Q2_yVrZgnxn-hcyWPa3QwX7AxZLLLuYKI_PIBY,4743
wikibase_rest_api_client/api/descriptions/get_item_description.py,sha256=rDFZctUSdq2nrjtJtCIUXPrJVjPMhSSG5OOKJ6hXptc,4786
wikibase_rest_api_client/api/descriptions/get_item_descriptions.py,sha256=tAtPr1g2cmBqm-h1XZU9QR2Usq23T08aIvDRsdq9al4,4549
wikibase_rest_api_client/api/descriptions/get_property_description.py,sha256=6GPgDFmHPlHv6AZmb9ruy2MG3VZeAFyYG6e68JiH8HI,4737
wikibase_rest_api_client/api/descriptions/get_property_descriptions.py,sha256=icNH1lJrJD5huY_FQ70lYcIrbfex1GBaT9AF80q5q8A,4612
wikibase_rest_api_client/api/descriptions/patch_item_descriptions.py,sha256=OvsPnn85oQXCRsdtgNPES9ZjLKxoq-0XGIKck-Zrg3E,3825
wikibase_rest_api_client/api/descriptions/patch_property_descriptions.py,sha256=AfZ87qFWiDPc8sjAUI68Vza1q0VhWzLhoKlLEh8Jq0g,3884
wikibase_rest_api_client/api/descriptions/replace_item_description.py,sha256=TdlwDUTY_DhY6I5OPBTl5yTiqCMB116fvz4Mfi5SbG4,5354
wikibase_rest_api_client/api/descriptions/replace_property_description.py,sha256=s7hPF_vPJlYvll79BFDhhCH7SEBGYLANf1SIaAhDAbY,5341
wikibase_rest_api_client/api/items/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
wikibase_rest_api_client/api/items/__pycache__/__init__.cpython-312.pyc,,
wikibase_rest_api_client/api/items/__pycache__/get_item.cpython-312.pyc,,
wikibase_rest_api_client/api/items/get_item.py,sha256=P6elkLsOD8bKcn-dcFwoOtqh3dn642fttk6F0D6ORKM,5474
wikibase_rest_api_client/api/labels/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
wikibase_rest_api_client/api/labels/__pycache__/__init__.cpython-312.pyc,,
wikibase_rest_api_client/api/labels/__pycache__/delete_item_label.cpython-312.pyc,,
wikibase_rest_api_client/api/labels/__pycache__/delete_property_label.cpython-312.pyc,,
wikibase_rest_api_client/api/labels/__pycache__/get_item_label.cpython-312.pyc,,
wikibase_rest_api_client/api/labels/__pycache__/get_item_label_with_language_fallback.cpython-312.pyc,,
wikibase_rest_api_client/api/labels/__pycache__/get_item_labels.cpython-312.pyc,,
wikibase_rest_api_client/api/labels/__pycache__/get_property_label.cpython-312.pyc,,
wikibase_rest_api_client/api/labels/__pycache__/get_property_labels.cpython-312.pyc,,
wikibase_rest_api_client/api/labels/__pycache__/patch_item_labels.cpython-312.pyc,,
wikibase_rest_api_client/api/labels/__pycache__/patch_property_labels.cpython-312.pyc,,
wikibase_rest_api_client/api/labels/__pycache__/replace_item_label.cpython-312.pyc,,
wikibase_rest_api_client/api/labels/__pycache__/replace_property_label.cpython-312.pyc,,
wikibase_rest_api_client/api/labels/delete_item_label.py,sha256=IdXqC8Mped4msAl-cna-_trNxdhllfMRuII2BEveeZ4,4546
wikibase_rest_api_client/api/labels/delete_property_label.py,sha256=8oBurJQtFmTEkH2APWhcHozaoGnLkjOqxIOWwxNuLsI,4533
wikibase_rest_api_client/api/labels/get_item_label.py,sha256=He7OS-iqscO2rKvXcaQcueN7RvgEIABDiEHAHfyXgyc,4670
wikibase_rest_api_client/api/labels/get_item_label_with_language_fallback.py,sha256=ojoNzpHYPWNnXuyruttaTDLgZNueRHo9pH1Zo_DetZE,4869
wikibase_rest_api_client/api/labels/get_item_labels.py,sha256=jqf1r9WzWx_zNgFTiqKwO5OwRgAo1VFK9HSune5R84o,4399
wikibase_rest_api_client/api/labels/get_property_label.py,sha256=X1R7eV_z7RPE7D4ugGLEgpmK0_9I4g56u5W6gNnEiHE,4647
wikibase_rest_api_client/api/labels/get_property_labels.py,sha256=TC74CUXYo6pEBNdB3AFtAwh5on5dU2AAPw-KWM4KxaI,4384
wikibase_rest_api_client/api/labels/patch_item_labels.py,sha256=ZJeubQ80iRj6RR5WXfnOkiWU2ungqddp10CU7ev-JFA,3771
wikibase_rest_api_client/api/labels/patch_property_labels.py,sha256=YZ86G2G31BTXZQiy7_H_1l-waEntyU9KYe2anU4Cy3Q,3802
wikibase_rest_api_client/api/labels/replace_item_label.py,sha256=1fsxeykXOx2x0L_x87oTGE9iL2LgyoCsEOTyHy24X-Y,5258
wikibase_rest_api_client/api/labels/replace_property_label.py,sha256=HPO2S6gT-xoCDZbSS4SekKYPfC9GIHolZ3NpgFiQYJo,5269
wikibase_rest_api_client/api/properties/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
wikibase_rest_api_client/api/properties/__pycache__/__init__.cpython-312.pyc,,
wikibase_rest_api_client/api/properties/__pycache__/get_property.cpython-312.pyc,,
wikibase_rest_api_client/api/properties/get_property.py,sha256=T7Iouz5NDUMU3pqLSqYK2-8gcaFgSy5MOBdP8G1vG0g,5388
wikibase_rest_api_client/api/search/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
wikibase_rest_api_client/api/search/__pycache__/__init__.cpython-312.pyc,,
wikibase_rest_api_client/api/search/__pycache__/search_item.cpython-312.pyc,,
wikibase_rest_api_client/api/search/search_item.py,sha256=JAN3t7xVaLLDCqzQuxHvaWcYCQmlRK9BNXDqeYhlbSg,6519
wikibase_rest_api_client/api/sitelinks/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
wikibase_rest_api_client/api/sitelinks/__pycache__/__init__.cpython-312.pyc,,
wikibase_rest_api_client/api/sitelinks/__pycache__/delete_item_sitelink.cpython-312.pyc,,
wikibase_rest_api_client/api/sitelinks/__pycache__/get_item_site_sitelink.cpython-312.pyc,,
wikibase_rest_api_client/api/sitelinks/__pycache__/get_item_sitelinks.cpython-312.pyc,,
wikibase_rest_api_client/api/sitelinks/__pycache__/patch_item_sitelinks.cpython-312.pyc,,
wikibase_rest_api_client/api/sitelinks/__pycache__/replace_sitelink.cpython-312.pyc,,
wikibase_rest_api_client/api/sitelinks/delete_item_sitelink.py,sha256=We2MM2TF39rI726MNbRK-f5sUu-0P55FINRhlm7hvYg,4840
wikibase_rest_api_client/api/sitelinks/get_item_site_sitelink.py,sha256=XjtkSlAG4FojF7ARa9sc-7oxrayMC4TOyT7TIpgdhkM,4670
wikibase_rest_api_client/api/sitelinks/get_item_sitelinks.py,sha256=P-EOz4spvBD4GSjiCTMn9O4rFc1b3_dsDY-02iY6lMY,4546
wikibase_rest_api_client/api/sitelinks/patch_item_sitelinks.py,sha256=vNeo9M4uzP6gSwfhb2MxHPZOkIgf9PUGKNjG4i7pDuA,4823
wikibase_rest_api_client/api/sitelinks/replace_sitelink.py,sha256=LAbPVK9BSh82VKlTdSf_NnyMGvqLXPmr_tX6KmoRehw,4910
wikibase_rest_api_client/api/statements/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
wikibase_rest_api_client/api/statements/__pycache__/__init__.cpython-312.pyc,,
wikibase_rest_api_client/api/statements/__pycache__/add_item_statement.cpython-312.pyc,,
wikibase_rest_api_client/api/statements/__pycache__/add_property_statement.cpython-312.pyc,,
wikibase_rest_api_client/api/statements/__pycache__/delete_statement.cpython-312.pyc,,
wikibase_rest_api_client/api/statements/__pycache__/get_item_statement.cpython-312.pyc,,
wikibase_rest_api_client/api/statements/__pycache__/get_item_statements.cpython-312.pyc,,
wikibase_rest_api_client/api/statements/__pycache__/get_property_statement.cpython-312.pyc,,
wikibase_rest_api_client/api/statements/__pycache__/get_property_statements.cpython-312.pyc,,
wikibase_rest_api_client/api/statements/__pycache__/get_statement.cpython-312.pyc,,
wikibase_rest_api_client/api/statements/__pycache__/patch_statement.cpython-312.pyc,,
wikibase_rest_api_client/api/statements/__pycache__/replace_statement.cpython-312.pyc,,
wikibase_rest_api_client/api/statements/add_item_statement.py,sha256=8lNhgMbu4XjZ6-Y3XzgMILhAygYQRcwn6c-IyXRLNPY,3900
wikibase_rest_api_client/api/statements/add_property_statement.py,sha256=cH7T1wjC5xqPWGq890yjulukrFofSQ-HWvq9j_3QYGE,3887
wikibase_rest_api_client/api/statements/delete_statement.py,sha256=5jmJvi_LzOEBsP6TVSyNzif-2ms2bs95jx-mZZ5V54A,3997
wikibase_rest_api_client/api/statements/get_item_statement.py,sha256=yqwb8raVzTLIwH7qJoo8eNGaNeRP2KSAGHnvVG1HeAs,4750
wikibase_rest_api_client/api/statements/get_item_statements.py,sha256=IFMTyPBSsDd7xQ1hqFS39fdu1K9SqeHc6XNOliug_F8,4866
wikibase_rest_api_client/api/statements/get_property_statement.py,sha256=O4Qvo_ZrxZ8EmPGngzFNlkm9e7CyyAOArV6Qs-7KP20,4809
wikibase_rest_api_client/api/statements/get_property_statements.py,sha256=DG_k4N11gT6sijJKFoFVdc3j4zClCl8mZloOV8t9o_Q,4851
wikibase_rest_api_client/api/statements/get_statement.py,sha256=Ew7pgadA5Nh-G-w-3G8ClWq7XBFX4Xxn881Adb-0bB8,4712
wikibase_rest_api_client/api/statements/patch_statement.py,sha256=Me6CnoHWJ-DvcmgAMg5LA-V--hnEYjql8EvfwfNGE84,4333
wikibase_rest_api_client/api/statements/replace_statement.py,sha256=Fr1OaDobWZjQz6xyRAZpGwVbQJh5ItwQpKC8zII5NoI,4163
wikibase_rest_api_client/client.py,sha256=Av0jqllPqTMD8NyrfwF8ZALGDqha2dNahZnb00KZ2hE,12237
wikibase_rest_api_client/errors.py,sha256=Z_n7BfRZBkLcjNOPhxn-EgOkLJtvNoKLaEgjP_k8XmA,468
wikibase_rest_api_client/models/__init__.py,sha256=PRC-ZKD57ycHlYvIwobcpdoCJGdSpxEt9SMyUVjiYSU,2962
wikibase_rest_api_client/models/__pycache__/__init__.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/aliases.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/aliases_add_request.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/aliases_patch_request.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/description_replace_request.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/descriptions.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/descriptions_patch_request.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/error.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/get_item_fields_item.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/get_property_fields_item.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/item.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/item_aliases.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/item_descriptions.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/item_labels.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/item_sitelinks.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/item_statements.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/label_replace_request.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/labels.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/labels_patch_request.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/language_value.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/mediawiki_edit.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/patch_document.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/patch_document_patch_item.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/patch_document_patch_item_op.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/property_.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/property_aliases.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/property_descriptions.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/property_info.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/property_labels.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/property_statements.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/qualifier.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/reference.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/search_item_result.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/search_item_results.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/search_match.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/sitelink.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/sitelink_patch_request.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/sitelink_replace_request.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/statement.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/statement_patch_request.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/statement_rank.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/statement_request.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/utils.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/value.cpython-312.pyc,,
wikibase_rest_api_client/models/__pycache__/value_type.cpython-312.pyc,,
wikibase_rest_api_client/models/aliases.py,sha256=PlgvkZRSEvUJ4JVfZZW_RjJCbF9lD6aFCyrGVT6d8Os,1593
wikibase_rest_api_client/models/aliases_add_request.py,sha256=3W97OVQ1CApqMSAEKxWE1tWtRZNXOKqPfcWUHeGiLfI,2495
wikibase_rest_api_client/models/aliases_patch_request.py,sha256=ViN8w-zbDqWOJGF_NsJ_zll4eFIYQdOjXOrjOrtFPQQ,3046
wikibase_rest_api_client/models/description_replace_request.py,sha256=1YyHZCbcD4Dv02hMmAaRdIA-H3VpYqA4M7txIofffpc,2549
wikibase_rest_api_client/models/descriptions.py,sha256=Jz78fZGmObhMw0JW9KXXMcqQx9cPkIJuy7P_f9lJVpA,1287
wikibase_rest_api_client/models/descriptions_patch_request.py,sha256=71ADAFqWAdDBQHHfSFzGkek_30Ne2zEHrHu2wvS8Xa4,2972
wikibase_rest_api_client/models/error.py,sha256=FD7OjWFl9VUFCtE9vOgPQgbkQYPAbfY9nzb8VrQ4ego,1557
wikibase_rest_api_client/models/get_item_fields_item.py,sha256=-wciiDhz1ohOZy6Le4DcrtbJenAzCKduCP8CNoGgfBY,278
wikibase_rest_api_client/models/get_property_fields_item.py,sha256=cLxSGwnwK9-Q-hsfy985SJo-RGFed3z3KM5awYyr0nY,282
wikibase_rest_api_client/models/item.py,sha256=uqUjSP4FKsPKMnGhEl9_0kyJA7VY-7Ra8UUKrrYRQ4M,5671
wikibase_rest_api_client/models/item_aliases.py,sha256=yWyBNduT97gikZm6FU8s2Hwr-29KGOHCGunl_s5nUSI,1277
wikibase_rest_api_client/models/item_descriptions.py,sha256=l39fZMuZC4ELgrwh1BAQnlWIiMYPuqBoiB623_tJXVo,1302
wikibase_rest_api_client/models/item_labels.py,sha256=luQdbrD5HkYeaKSVNuKopOF47DSjeOsVbSCJ0M8gQew,1272
wikibase_rest_api_client/models/item_sitelinks.py,sha256=MaNkjWk3SYSdg4pg4gcW3j-Zib5reIkEg6gef1d0eiI,2076
wikibase_rest_api_client/models/item_statements.py,sha256=uJFSIjYJEQ2umPfHc0RMCidlQS6yRhmzIST6cNqb9R0,2147
wikibase_rest_api_client/models/label_replace_request.py,sha256=uK9gwnegBGVAz8y3fw-niOHht106VZXfgI8EwFYcvT8,2465
wikibase_rest_api_client/models/labels.py,sha256=CjiMNaOH0DR3ZZYeXXVybkv1vdy576XEyxw8G6v4mNE,1245
wikibase_rest_api_client/models/labels_patch_request.py,sha256=qC4lARcPPnpbTZEoCtLdprpPCseWwToII5TBmx8wtLg,3041
wikibase_rest_api_client/models/language_value.py,sha256=gDOYSDJbld6cjpnTKDczqgaAyXpZpKqHbvejW7acETY,1708
wikibase_rest_api_client/models/mediawiki_edit.py,sha256=57_hacDmOzQ64Kwias3Eb4VtH4QHgc1d_-zBokHNbrM,2243
wikibase_rest_api_client/models/patch_document.py,sha256=AFntu0l4ULxNb2KgZhN7_A-w4Ux1WWILq5GnZb6Uc7k,2019
wikibase_rest_api_client/models/patch_document_patch_item.py,sha256=vknR8r2K18Vmd46ozO7rMV4IU-RFMRTpGuXU0Gqj0X0,2157
wikibase_rest_api_client/models/patch_document_patch_item_op.py,sha256=YyEB9Qq24XBIfFDTWV18VJmZ1I1GVxIdppJ3uzGmr-8,245
wikibase_rest_api_client/models/property_.py,sha256=Q5ktk5AMRgpheF8rf3LIZGWqMWtHZD-jArA1skNDdaw,5076
wikibase_rest_api_client/models/property_aliases.py,sha256=mou6aSkh9Ak-4TxMXsSKGgnm2ubDE8NUdvEDEdZG23U,1230
wikibase_rest_api_client/models/property_descriptions.py,sha256=KxZ6mfqLB8s5q6oLTa_huAMCiudi3SZcrd9WJsrZ4yQ,1255
wikibase_rest_api_client/models/property_info.py,sha256=t5VSnPAnFXAxzB-uhnZOfAnUaYSMWRsY85VZ-cMzKeQ,2433
wikibase_rest_api_client/models/property_labels.py,sha256=XaBdGD9Hm2xl6DIM5oXW8KymKy0OHV_6CMREQbst9gA,1225
wikibase_rest_api_client/models/property_statements.py,sha256=R_3kEB2KTj5JqtT1U_iR_Z7IL24ZSHKxIg_ILirJgOM,2167
wikibase_rest_api_client/models/qualifier.py,sha256=kGJhOBZX6AFMPo3HB3U3r1dntJ1zom55M6QQf4Fy0lI,2577
wikibase_rest_api_client/models/reference.py,sha256=Yqu-4io0R-K787vYSGgPNCBCa9-c_C-Tg0kNt1KmGyg,2374
wikibase_rest_api_client/models/search_item_result.py,sha256=11Z5OLchffpg3lCwOPUsiIoiCB_zaCgZgevRjAYKtxw,2805
wikibase_rest_api_client/models/search_item_results.py,sha256=ZmEV42USwIP4QplKqjj2dPCTwqSIfHBgnSQf0PjfKHU,2027
wikibase_rest_api_client/models/search_match.py,sha256=Hk6w0ryxZwhzkWHhvpElmrlimee9Qr5eFqODslK9Xe4,1892
wikibase_rest_api_client/models/sitelink.py,sha256=lSM_COL0QOTRT_vbe-X15XkcHFnODzCX3b0awptwojw,2090
wikibase_rest_api_client/models/sitelink_patch_request.py,sha256=K3NU0cB-10jTFuZ69dYUpFwbJkjtEy374DcD-7pukAY,2955
wikibase_rest_api_client/models/sitelink_replace_request.py,sha256=yhv1eiyOTXGPO4Yhb00PTdylp08wx5fitRgGWykRLS4,2773
wikibase_rest_api_client/models/statement.py,sha256=O9A6Oc4GEK6bsvUat0s2STrWEBiMRmuS_Te6m9zp83I,5292
wikibase_rest_api_client/models/statement_patch_request.py,sha256=FBjCXa-FXKkvGKMqAdc0LqGrQaClZU3Rng421vHlFCo,3056
wikibase_rest_api_client/models/statement_rank.py,sha256=TVikCH0-QoF5xVfRu_SY2reKLgP60l2MWbn919T32WI,198
wikibase_rest_api_client/models/statement_request.py,sha256=fZKEHm8Oe4deYUz6qaLwTb7f--vX5CaJwc4-m5R5SQI,2661
wikibase_rest_api_client/models/utils.py,sha256=6WPH2Va6nqyLu69KBkihe3fimckD4MdJZ8CVqxUTdfM,293
wikibase_rest_api_client/models/value.py,sha256=Ux4d-lQEjokpQPPruNk9FcrU3gqSCCXLcj240_-_PjI,2136
wikibase_rest_api_client/models/value_type.py,sha256=xdpoTNfY9Fsn2ate0eoLbkpiwp1KKskQAbT0x7aVmOM,186
wikibase_rest_api_client/py.typed,sha256=8ZJUsxZiuOy1oJeVhsTWQhTG_6pTVHVXk5hJL79ebTk,25
wikibase_rest_api_client/types.py,sha256=5kDODqBvJgajfpZQEPW8gE5E7OCPl1i17SFJSmegJco,967
wikibase_rest_api_client/utilities/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
wikibase_rest_api_client/utilities/__pycache__/__init__.cpython-312.pyc,,
wikibase_rest_api_client/utilities/__pycache__/fluent.cpython-312.pyc,,
wikibase_rest_api_client/utilities/fluent.py,sha256=9_J15lkp-aLs0a-mY1z_Zfdj8n74e9RlUfytCRNfT_w,6253
